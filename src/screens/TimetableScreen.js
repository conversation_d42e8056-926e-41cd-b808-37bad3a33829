import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faArrowLeft, faCalendarAlt, faClock } from '@fortawesome/free-solid-svg-icons';
import timetableData from '../data/dummyTimetable.json';

export default function TimetableScreen({ navigation, route }) {
  const { studentName } = route.params || {};
  const [selectedDay, setSelectedDay] = useState('Monday');
  
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
  
  const getSubjectColor = (subject) => {
    const colors = {
      'Math': '#FF6B6B',
      'English': '#4ECDC4',
      'Physics': '#45B7D1',
      'Chemistry': '#96CEB4',
      'Biology': '#FFEAA7',
      'History': '#DDA0DD',
      'Geography': '#98D8C8',
      'Art': '#F7DC6F',
      'Music': '#BB8FCE',
      'PE': '#85C1E9',
      'Physical Education': '#85C1E9',
      'Computer Science': '#82E0AA',
      'Computer Lab': '#82E0AA',
      'Health': '#F8C471',
      'Drama': '#D7BDE2',
    };
    return colors[subject] || '#BDC3C7';
  };

  const renderTimeSlot = ({ item, index }) => (
    <View style={styles.timeSlotContainer}>
      <View style={styles.timeContainer}>
        <FontAwesomeIcon icon={faClock} size={16} color="#666" />
        <Text style={styles.timeText}>{item.time}</Text>
      </View>
      <View style={[styles.subjectContainer, { backgroundColor: getSubjectColor(item.subject) }]}>
        <Text style={styles.subjectText}>{item.subject}</Text>
      </View>
    </View>
  );

  const renderDayTab = (day) => (
    <TouchableOpacity
      key={day}
      style={[
        styles.dayTab,
        selectedDay === day && styles.selectedDayTab
      ]}
      onPress={() => setSelectedDay(day)}
    >
      <Text style={[
        styles.dayTabText,
        selectedDay === day && styles.selectedDayTabText
      ]}>
        {day.substring(0, 3)}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <FontAwesomeIcon icon={faArrowLeft} size={20} color="#fff" />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <FontAwesomeIcon icon={faCalendarAlt} size={20} color="#fff" />
          <Text style={styles.headerTitle}>Timetable</Text>
        </View>
        <View style={styles.headerRight} />
      </View>

      {studentName && (
        <View style={styles.studentInfo}>
          <Text style={styles.studentNameText}>Schedule for {studentName}</Text>
        </View>
      )}

      <View style={styles.content}>
        {/* Day Tabs */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.dayTabsContainer}
          contentContainerStyle={styles.dayTabsContent}
        >
          {days.map(renderDayTab)}
        </ScrollView>

        {/* Selected Day Schedule */}
        <View style={styles.scheduleContainer}>
          <Text style={styles.dayTitle}>{selectedDay}</Text>
          <FlatList
            data={timetableData[selectedDay]}
            renderItem={renderTimeSlot}
            keyExtractor={(item, index) => `${selectedDay}-${index}`}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scheduleList}
          />
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#AF52DE',
    padding: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  headerRight: {
    width: 36,
  },
  studentInfo: {
    backgroundColor: '#fff',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  studentNameText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  dayTabsContainer: {
    marginBottom: 20,
  },
  dayTabsContent: {
    paddingHorizontal: 5,
  },
  dayTab: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    marginHorizontal: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedDayTab: {
    backgroundColor: '#AF52DE',
  },
  dayTabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  selectedDayTabText: {
    color: '#fff',
  },
  scheduleContainer: {
    flex: 1,
  },
  dayTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  scheduleList: {
    paddingBottom: 20,
  },
  timeSlotContainer: {
    flexDirection: 'row',
    marginBottom: 15,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 120,
    paddingRight: 15,
  },
  timeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginLeft: 8,
  },
  subjectContainer: {
    flex: 1,
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 8,
    justifyContent: 'center',
  },
  subjectText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
});
